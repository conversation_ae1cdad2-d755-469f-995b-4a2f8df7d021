a:6:{s:9:"classesIn";a:1:{s:40:"Nzoom\Export\Provider\ModelTableProvider";a:6:{s:4:"name";s:18:"ModelTableProvider";s:14:"namespacedName";s:40:"Nzoom\Export\Provider\ModelTableProvider";s:9:"namespace";s:21:"Nzoom\Export\Provider";s:9:"startLine";i:18;s:7:"endLine";i:907;s:7:"methods";a:25:{s:11:"__construct";a:6:{s:10:"methodName";s:11:"__construct";s:9:"signature";s:106:"__construct(Registry $registry, string $referenceColumnName, string $referenceColumnLabel, array $options)";s:10:"visibility";s:6:"public";s:9:"startLine";i:50;s:7:"endLine";i:58;s:3:"ccn";i:1;}s:18:"getReferenceColumn";a:6:{s:10:"methodName";s:18:"getReferenceColumn";s:9:"signature";s:27:"getReferenceColumn(): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:65;s:7:"endLine";i:68;s:3:"ccn";i:1;}s:17:"getDefaultOptions";a:6:{s:10:"methodName";s:17:"getDefaultOptions";s:9:"signature";s:26:"getDefaultOptions(): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:75;s:7:"endLine";i:82;s:3:"ccn";i:1;}s:18:"getTablesForRecord";a:6:{s:10:"methodName";s:18:"getTablesForRecord";s:9:"signature";s:86:"getTablesForRecord($record, array $options): Nzoom\Export\Entity\ExportTableCollection";s:10:"visibility";s:6:"public";s:9:"startLine";i:87;s:7:"endLine";i:122;s:3:"ccn";i:10;}s:25:"discoverGroupingVariables";a:6:{s:10:"methodName";s:25:"discoverGroupingVariables";s:9:"signature";s:46:"discoverGroupingVariables(Model $model): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:130;s:7:"endLine";i:171;s:3:"ccn";i:10;}s:27:"createTableFromGroupingData";a:6:{s:10:"methodName";s:27:"createTableFromGroupingData";s:9:"signature";s:129:"createTableFromGroupingData(Model $model, string $varName, array $groupingData, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:182;s:7:"endLine";i:223;s:3:"ccn";i:6;}s:22:"createTableFromGT2Data";a:6:{s:10:"methodName";s:22:"createTableFromGT2Data";s:9:"signature";s:119:"createTableFromGT2Data(Model $model, string $varName, array $gt2Data, array $options): ?Nzoom\Export\Entity\ExportTable";s:10:"visibility";s:7:"private";s:9:"startLine";i:234;s:7:"endLine";i:273;s:3:"ccn";i:5;}s:22:"getOrCreateTableHeader";a:6:{s:10:"methodName";s:22:"getOrCreateTableHeader";s:9:"signature";s:133:"getOrCreateTableHeader(string $tableType, array $names, array $labels, array $hidden, array $types): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:285;s:7:"endLine";i:338;s:3:"ccn";i:7;}s:25:"getOrCreateGT2TableHeader";a:6:{s:10:"methodName";s:25:"getOrCreateGT2TableHeader";s:9:"signature";s:91:"getOrCreateGT2TableHeader(string $tableType, array $vars): Nzoom\Export\Entity\ExportHeader";s:10:"visibility";s:7:"private";s:9:"startLine";i:347;s:7:"endLine";i:398;s:3:"ccn";i:6;}s:21:"sortGT2VarsByPosition";a:6:{s:10:"methodName";s:21:"sortGT2VarsByPosition";s:9:"signature";s:41:"sortGT2VarsByPosition(array $vars): array";s:10:"visibility";s:7:"private";s:9:"startLine";i:406;s:7:"endLine";i:427;s:3:"ccn";i:5;}s:15:"formatTableName";a:6:{s:10:"methodName";s:15:"formatTableName";s:9:"signature";s:40:"formatTableName(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:435;s:7:"endLine";i:442;s:3:"ccn";i:1;}s:27:"convertFieldTypeToValueType";a:6:{s:10:"methodName";s:27:"convertFieldTypeToValueType";s:9:"signature";s:54:"convertFieldTypeToValueType(string $fieldType): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:450;s:7:"endLine";i:475;s:3:"ccn";i:13;}s:15:"guessColumnType";a:6:{s:10:"methodName";s:15:"guessColumnType";s:9:"signature";s:40:"guessColumnType(string $varName): string";s:10:"visibility";s:7:"private";s:9:"startLine";i:483;s:7:"endLine";i:510;s:3:"ccn";i:6;}s:29:"populateTableFromGroupingData";a:6:{s:10:"methodName";s:29:"populateTableFromGroupingData";s:9:"signature";s:184:"populateTableFromGroupingData(Nzoom\Export\Entity\ExportTable $table, array $values, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:524;s:7:"endLine";i:536;s:3:"ccn";i:3;}s:24:"populateTableFromGT2Data";a:6:{s:10:"methodName";s:24:"populateTableFromGT2Data";s:9:"signature";s:128:"populateTableFromGT2Data(Nzoom\Export\Entity\ExportTable $table, array $values, array $vars, array $options, Model $model): void";s:10:"visibility";s:7:"private";s:9:"startLine";i:547;s:7:"endLine";i:562;s:3:"ccn";i:3;}s:26:"createRecordFromGT2RowData";a:6:{s:10:"methodName";s:26:"createRecordFromGT2RowData";s:9:"signature";s:149:"createRecordFromGT2RowData(array $rowData, array $sortedVars, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:574;s:7:"endLine";i:609;s:3:"ccn";i:5;}s:23:"createRecordFromRowData";a:6:{s:10:"methodName";s:23:"createRecordFromRowData";s:9:"signature";s:191:"createRecordFromRowData(array $rowData, array $names, array $hidden, array $types, array $groupingData, array $options, Model $model, int $enumerationValue): ?Nzoom\Export\Entity\ExportRecord";s:10:"visibility";s:7:"private";s:9:"startLine";i:624;s:7:"endLine";i:665;s:3:"ccn";i:6;}s:18:"resolveOptionLabel";a:6:{s:10:"methodName";s:18:"resolveOptionLabel";s:9:"signature";s:84:"resolveOptionLabel($value, ?string $fieldType, string $varName, array $groupingData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:678;s:7:"endLine";i:702;s:3:"ccn";i:8;}s:29:"resolveOptionLabelFromVarData";a:6:{s:10:"methodName";s:29:"resolveOptionLabelFromVarData";s:9:"signature";s:72:"resolveOptionLabelFromVarData($value, string $fieldType, array $varData)";s:10:"visibility";s:7:"private";s:9:"startLine";i:712;s:7:"endLine";i:736;s:3:"ccn";i:8;}s:11:"formatValue";a:6:{s:10:"methodName";s:11:"formatValue";s:9:"signature";s:66:"formatValue($value, string $type, ?string $format, array $options)";s:10:"visibility";s:7:"private";s:9:"startLine";i:747;s:7:"endLine";i:783;s:3:"ccn";i:15;}s:21:"getTableConfiguration";a:6:{s:10:"methodName";s:21:"getTableConfiguration";s:9:"signature";s:47:"getTableConfiguration(string $tableType): array";s:10:"visibility";s:6:"public";s:9:"startLine";i:788;s:7:"endLine";i:795;s:3:"ccn";i:1;}s:14:"validateRecord";a:6:{s:10:"methodName";s:14:"validateRecord";s:9:"signature";s:57:"validateRecord($record, array $requestedTableTypes): bool";s:10:"visibility";s:6:"public";s:9:"startLine";i:800;s:7:"endLine";i:809;s:3:"ccn";i:2;}s:15:"shouldSkipTable";a:6:{s:10:"methodName";s:15:"shouldSkipTable";s:9:"signature";s:61:"shouldSkipTable(Nzoom\Export\Entity\ExportTable $table): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:817;s:7:"endLine";i:828;s:3:"ccn";i:2;}s:15:"isTableRowEmpty";a:6:{s:10:"methodName";s:15:"isTableRowEmpty";s:9:"signature";s:63:"isTableRowEmpty(Nzoom\Export\Entity\ExportRecord $record): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:837;s:7:"endLine";i:858;s:3:"ccn";i:7;}s:18:"isValueEmptyOrZero";a:6:{s:10:"methodName";s:18:"isValueEmptyOrZero";s:9:"signature";s:32:"isValueEmptyOrZero($value): bool";s:10:"visibility";s:7:"private";s:9:"startLine";i:866;s:7:"endLine";i:906;s:3:"ccn";i:8;}}}}s:8:"traitsIn";a:0:{}s:11:"functionsIn";a:0:{}s:14:"linesOfCodeFor";a:3:{s:11:"linesOfCode";i:908;s:18:"commentLinesOfCode";i:283;s:21:"nonCommentLinesOfCode";i:625;}s:15:"ignoredLinesFor";a:1:{i:0;i:18;}s:17:"executableLinesIn";a:325:{i:50;i:5;i:52;i:6;i:53;i:7;i:54;i:7;i:55;i:7;i:56;i:7;i:57;i:8;i:67;i:9;i:77;i:10;i:78;i:10;i:79;i:10;i:80;i:10;i:81;i:10;i:87;i:11;i:89;i:12;i:90;i:13;i:92;i:14;i:93;i:15;i:98;i:16;i:100;i:17;i:102;i:18;i:103;i:19;i:105;i:20;i:108;i:21;i:109;i:22;i:112;i:23;i:114;i:24;i:115;i:25;i:116;i:25;i:117;i:25;i:121;i:26;i:132;i:27;i:138;i:28;i:139;i:29;i:143;i:30;i:144;i:31;i:145;i:32;i:148;i:33;i:151;i:34;i:152;i:35;i:156;i:36;i:157;i:37;i:158;i:38;i:161;i:39;i:163;i:40;i:164;i:41;i:165;i:41;i:166;i:41;i:170;i:42;i:185;i:43;i:186;i:44;i:187;i:45;i:188;i:46;i:189;i:47;i:191;i:48;i:192;i:49;i:196;i:50;i:199;i:51;i:200;i:52;i:203;i:53;i:204;i:53;i:205;i:53;i:206;i:53;i:207;i:53;i:208;i:53;i:209;i:53;i:210;i:53;i:211;i:53;i:212;i:53;i:215;i:54;i:218;i:55;i:219;i:56;i:222;i:57;i:237;i:58;i:238;i:59;i:240;i:60;i:241;i:61;i:245;i:62;i:248;i:63;i:249;i:64;i:252;i:65;i:253;i:65;i:254;i:65;i:255;i:65;i:256;i:65;i:257;i:65;i:258;i:65;i:259;i:65;i:260;i:65;i:261;i:65;i:262;i:65;i:265;i:66;i:268;i:67;i:269;i:68;i:272;i:69;i:285;i:70;i:288;i:71;i:289;i:72;i:293;i:73;i:296;i:74;i:297;i:74;i:298;i:74;i:299;i:74;i:300;i:74;i:301;i:75;i:304;i:76;i:305;i:76;i:306;i:76;i:307;i:76;i:308;i:76;i:309;i:77;i:311;i:78;i:313;i:79;i:314;i:80;i:317;i:81;i:319;i:82;i:321;i:83;i:322;i:84;i:324;i:85;i:327;i:86;i:330;i:87;i:331;i:88;i:335;i:89;i:337;i:90;i:350;i:91;i:351;i:92;i:355;i:93;i:358;i:94;i:359;i:94;i:360;i:94;i:361;i:94;i:362;i:94;i:363;i:95;i:366;i:96;i:367;i:96;i:368;i:96;i:369;i:96;i:370;i:96;i:371;i:97;i:374;i:98;i:376;i:99;i:378;i:100;i:379;i:101;i:382;i:102;i:384;i:103;i:385;i:104;i:387;i:105;i:390;i:106;i:391;i:107;i:395;i:108;i:397;i:109;i:409;i:110;i:410;i:111;i:411;i:112;i:412;i:113;i:416;i:114;i:419;i:115;i:420;i:116;i:421;i:117;i:422;i:118;i:426;i:119;i:438;i:120;i:439;i:121;i:441;i:122;i:453;i:123;i:454;i:124;i:456;i:125;i:457;i:126;i:459;i:127;i:460;i:128;i:461;i:129;i:462;i:130;i:463;i:131;i:464;i:132;i:465;i:133;i:466;i:134;i:467;i:135;i:471;i:136;i:473;i:137;i:485;i:138;i:488;i:139;i:489;i:140;i:490;i:141;i:492;i:142;i:496;i:143;i:497;i:144;i:498;i:145;i:500;i:146;i:504;i:147;i:505;i:148;i:509;i:149;i:527;i:150;i:529;i:151;i:530;i:152;i:531;i:153;i:532;i:154;i:533;i:155;i:550;i:156;i:553;i:157;i:555;i:158;i:556;i:159;i:557;i:160;i:558;i:161;i:559;i:162;i:576;i:163;i:579;i:164;i:580;i:165;i:583;i:166;i:585;i:167;i:587;i:168;i:588;i:169;i:592;i:170;i:593;i:171;i:595;i:172;i:596;i:173;i:598;i:174;i:602;i:175;i:603;i:176;i:605;i:177;i:608;i:178;i:626;i:179;i:629;i:180;i:630;i:181;i:633;i:182;i:635;i:183;i:637;i:184;i:638;i:185;i:642;i:186;i:644;i:187;i:645;i:188;i:646;i:189;i:648;i:190;i:649;i:191;i:651;i:192;i:654;i:193;i:658;i:194;i:659;i:195;i:661;i:196;i:664;i:197;i:681;i:198;i:682;i:199;i:686;i:200;i:687;i:201;i:690;i:202;i:693;i:203;i:694;i:204;i:695;i:205;i:696;i:206;i:701;i:207;i:715;i:208;i:716;i:209;i:720;i:210;i:721;i:211;i:724;i:212;i:727;i:213;i:728;i:214;i:729;i:215;i:730;i:216;i:735;i:217;i:749;i:218;i:750;i:219;i:754;i:220;i:755;i:221;i:756;i:222;i:757;i:223;i:758;i:224;i:759;i:225;i:761;i:226;i:763;i:227;i:764;i:228;i:765;i:229;i:766;i:230;i:767;i:231;i:768;i:232;i:770;i:233;i:772;i:234;i:773;i:235;i:775;i:236;i:776;i:237;i:778;i:238;i:779;i:239;i:782;i:240;i:791;i:241;i:792;i:241;i:793;i:241;i:794;i:241;i:800;i:242;i:802;i:243;i:803;i:244;i:808;i:245;i:820;i:246;i:821;i:247;i:824;i:248;i:825;i:249;i:827;i:250;i:839;i:251;i:840;i:252;i:843;i:253;i:845;i:254;i:846;i:255;i:848;i:256;i:849;i:257;i:852;i:258;i:853;i:259;i:857;i:260;i:869;i:261;i:870;i:262;i:874;i:263;i:875;i:264;i:879;i:265;i:880;i:266;i:884;i:267;i:885;i:268;i:889;i:269;i:890;i:270;i:891;i:270;i:892;i:270;i:893;i:270;i:894;i:270;i:895;i:270;i:896;i:270;i:897;i:270;i:899;i:271;i:900;i:272;i:905;i:273;}}