<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/Nzoom-Hella/_libs/Nzoom/Export/Streamer</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=9.2.32" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/Nzoom-Hella/_libs/Nzoom/Export</a></li>
         <li class="breadcrumb-item active">Streamer</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="67.55" aria-valuemin="0" aria-valuemax="100" style="width: 67.55%">
           <span class="sr-only">67.55% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">67.55%</div></td>
       <td class="warning small"><div align="right">102&nbsp;/&nbsp;151</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="51.16" aria-valuemin="0" aria-valuemax="100" style="width: 51.16%">
           <span class="sr-only">51.16% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">51.16%</div></td>
       <td class="warning small"><div align="right">22&nbsp;/&nbsp;43</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;4</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="FileStreamer.php.html">FileStreamer.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="78.95" aria-valuemin="0" aria-valuemax="100" style="width: 78.95%">
           <span class="sr-only">78.95% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">78.95%</div></td>
       <td class="success small"><div align="right">30&nbsp;/&nbsp;38</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="56.25" aria-valuemin="0" aria-valuemax="100" style="width: 56.25%">
           <span class="sr-only">56.25% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">56.25%</div></td>
       <td class="warning small"><div align="right">9&nbsp;/&nbsp;16</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><img src="../_icons/file-code.svg" class="octicon" /><a href="GeneratorFileStreamer.php.html">GeneratorFileStreamer.php</a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="20.59" aria-valuemin="0" aria-valuemax="100" style="width: 20.59%">
           <span class="sr-only">20.59% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">20.59%</div></td>
       <td class="danger small"><div align="right">7&nbsp;/&nbsp;34</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;7</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="PointerFileStreamer.php.html">PointerFileStreamer.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="72.73" aria-valuemin="0" aria-valuemax="100" style="width: 72.73%">
           <span class="sr-only">72.73% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">72.73%</div></td>
       <td class="success small"><div align="right">24&nbsp;/&nbsp;33</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="16.67" aria-valuemin="0" aria-valuemax="100" style="width: 16.67%">
           <span class="sr-only">16.67% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">16.67%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-code.svg" class="octicon" /><a href="StreamHeaders.php.html">StreamHeaders.php</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="89.13" aria-valuemin="0" aria-valuemax="100" style="width: 89.13%">
           <span class="sr-only">89.13% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">89.13%</div></td>
       <td class="success small"><div align="right">41&nbsp;/&nbsp;46</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="85.71" aria-valuemin="0" aria-valuemax="100" style="width: 85.71%">
           <span class="sr-only">85.71% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">85.71%</div></td>
       <td class="success small"><div align="right">12&nbsp;/&nbsp;14</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class=""><img src="../_icons/file-code.svg" class="octicon" /><a href="StreamerInterface.php.html">StreamerInterface.php</a></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 35%</span>
     <span class="warning"><strong>Medium</strong>: 35% to 70%</span>
     <span class="success"><strong>High</strong>: 70% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 9.2.32</a> using <a href="https://secure.php.net/" target="_top">PHP 7.4.33</a> and <a href="https://phpunit.de/">PHPUnit 9.6.23</a> at Wed Jun 25 7:42:26 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
