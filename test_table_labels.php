<?php

// Simple test without full framework bootstrap
require_once '_libs/vendor/autoload.php';
require_once '_libs/inc/common/registry.class.php';
require_once '_libs/inc/mvc/model.class.php';

use Nzoom\Export\Provider\ModelTableProvider;

echo "Testing table labels functionality...\n\n";

// Create registry
$registry = new Registry();

// Create mock model with grouping data that includes labels
$varsForTemplate = [
    'purchase_history' => [
        'type' => 'grouping',
        'label' => 'Purchase History', // This should be used as the Excel sheet name
        'names' => ['item_name', 'quantity', 'price'],
        'labels' => ['Item Name', 'Quantity', 'Price'],
        'hidden' => [],
        'types' => ['text', 'text', 'text'],
        'values' => [
            ['Laptop Pro', '1', '1299.99'],
            ['Wireless Mouse', '2', '49.99'],
            ['USB Cable', '3', '15.99']
        ]
    ],
    'order_details' => [
        'type' => 'gt2',
        'label' => 'Order Details', // This should be used as the Excel sheet name
        'vars' => [
            'product_code' => [
                'position' => 1,
                'hidden' => '0',
                'label' => 'Product Code',
                'type' => 'text'
            ],
            'description' => [
                'position' => 2,
                'hidden' => '0',
                'label' => 'Description',
                'type' => 'text'
            ],
            'unit_price' => [
                'position' => 3,
                'hidden' => '0',
                'label' => 'Unit Price',
                'type' => 'text'
            ]
        ],
        'values' => [
            'row1' => [
                'product_code' => 'LP001',
                'description' => 'High-performance laptop',
                'unit_price' => '1299.99'
            ],
            'row2' => [
                'product_code' => 'MS002',
                'description' => 'Wireless optical mouse',
                'unit_price' => '49.99'
            ]
        ]
    ],
    'no_label_table' => [
        'type' => 'grouping',
        // No 'label' field - should fallback to formatted variable name
        'names' => ['category', 'count'],
        'labels' => ['Category', 'Count'],
        'hidden' => [],
        'values' => [
            ['Electronics', '5'],
            ['Accessories', '3']
        ]
    ],
    'empty_label_table' => [
        'type' => 'grouping',
        'label' => '', // Empty string should fallback to formatted variable name
        'names' => ['type', 'amount'],
        'labels' => ['Type', 'Amount'],
        'hidden' => [],
        'values' => [
            ['Service', '100.00']
        ]
    ],
    'whitespace_label_table' => [
        'type' => 'grouping',
        'label' => '   ', // Whitespace-only should fallback to formatted variable name
        'names' => ['code', 'description'],
        'labels' => ['Code', 'Description'],
        'hidden' => [],
        'values' => [
            ['ABC123', 'Test item']
        ]
    ],
    'trimmed_label_table' => [
        'type' => 'grouping',
        'label' => '  Trimmed Label  ', // Should be trimmed
        'names' => ['name', 'value'],
        'labels' => ['Name', 'Value'],
        'hidden' => [],
        'values' => [
            ['Test', '42']
        ]
    ]
];

// Create a proper model instance
$model = new Model(
    ['id' => 1, 'full_num' => 'DOC-001'], // data
    [], // exportValues
    [], // exportTypes
    $varsForTemplate // varsForTemplate
);

echo "Creating ModelTableProvider...\n";
$provider = new ModelTableProvider($registry, 'full_num', 'Full Num');

echo "Getting tables for record...\n";
$collection = $provider->getTablesForRecord($model);

echo "Found " . $collection->count() . " tables:\n";
foreach ($collection->getTables() as $table) {
    echo "- Table Type: " . $table->getTableType() . "\n";
    echo "  Table Name: " . $table->getTableName() . "\n";
    echo "  Records: " . $table->count() . "\n\n";
}

echo "Test completed successfully!\n";
echo "\nExpected results:\n";
echo "- 'purchase_history' table should have name 'Purchase History' (from label)\n";
echo "- 'order_details' table should have name 'Order Details' (from label)\n";
echo "- 'no_label_table' table should have name 'No Label Table' (fallback to formatted name)\n";
echo "- 'empty_label_table' table should have name 'Empty Label Table' (empty string fallback)\n";
echo "- 'whitespace_label_table' table should have name 'Whitespace Label Table' (whitespace fallback)\n";
echo "- 'trimmed_label_table' table should have name 'Trimmed Label' (trimmed whitespace)\n";
