    <table border="1" cellpadding="3" cellspacing="0">
        <tr>
          <th nowrap="nowrap">{#num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name|default:#customers_name#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.city|default:#customers_city#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.skype|default:#customers_skype#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.department|default:#customers_department#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.type|default:#customers_type#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.email|default:#customers_email#|escape}</th>
          <th nowrap="nowrap">{#customers_company_person#|escape}</th>
          <th nowrap="nowrap">{#customers_group#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.position_name}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.working_to}</th>
          <th nowrap="nowrap">{$basic_vars_labels.added|default:#added#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.link_contact_persons_name}</th>
          <th nowrap="nowrap">{$basic_vars_labels.gsm|default:#customers_gsm#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.num|default:#customers_num#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.main_trademark|default:#customers_main_trademark#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.tags|default:#customers_tags#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.phone|default:#customers_phone#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.web|default:#customers_web#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.abilitys}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.ticket_email_from}</th>
          <th nowrap="nowrap">{$basic_vars_labels.othercontact|default:#customers_othercontact#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.ucn|default:#customers_ucn#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.name_code|default:#customers_name_code#|escape}</th>
          <th nowrap="nowrap">{$basic_vars_labels.comments|default:#customers_comments#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.working_from}</th>
          <th nowrap="nowrap">{$basic_vars_labels.emails|default:#customers_emails#|escape}</th>
          <th nowrap="nowrap">{$add_vars_labels.1.empl_picture}</th>

        </tr>
      {foreach name='i' from=$customers item='single'}
        {if !$single->checkPermissions('list')}
          <tr>
            <td nowrap="nowrap">{counter name='item_counter' print=true}</td>
            <td colspan="30-1">{#error_right_notallowed#|escape}</td>
          </tr>
        {else}
          <tr valign="top">
            <td>{counter name='item_counter' print=true}</td>
          <td style="mso-number-format: \@;">{$salutation}{$single->get('name')|escape}{if !$single->get('is_company')} {$single->get('lastname')|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('city')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
          {if is_array($single->get('skype'))}
            {foreach from=$single->get('skype') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}, {/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('department_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('type_name')|escape|default:"&nbsp;"}</td>
          <td{if !$single->get('email')} {/if}>
            {if is_array($single->get('email'))}
              {foreach from=$single->get('email') item='email' name='cdi'}
                {$email|escape|default:"&nbsp;"}{if !$smarty.foreach.cdi.last}, {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">{if $single->get('is_company')}{#customers_company#|escape}{else}{#customers_person#|escape}{/if}</td>
          <td style="mso-number-format: \@;">{$single->get('group_name')|escape|default:"&nbsp;"}</td>
          {capture assign='var_value'}{$single->getVarValue('position_name')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.position_name}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          {capture assign='var_value'}{$single->getVarValue('working_to')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.working_to}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          <td nowrap="nowrap">{$single->get('added')|date_format:#date_short#|escape}</td>
          {capture assign='var_value'}{$single->getVarValue('link_contact_persons_name')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.link_contact_persons_name}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('gsm') item='gsm' name='cdi'}
              {$gsm|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td style="mso-number-format: \@;">{$single->get('num')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">{$single->get('main_trademark_name')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">
            {if $single->get('model_tags') && is_array($single->get('model_tags')) && $single->get('model_tags')|@count gt 0 && $single->checkPermissions('tags_view')}
              {foreach from=$single->get('model_tags') item='tag' name='ti'}
                {$tag->get('name')|escape}{if !$smarty.foreach.ti.last}, {/if}
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          <td style="mso-number-format: \@;">
            {foreach from=$single->get('phone') item='phone' name='cdi'}
              {$phone|escape|default:'&nbsp;'}{if !$smarty.foreach.cdi.last},{/if}
            {/foreach}
          </td>
          <td{if !$single->get('web')} {/if}>
            {if is_array($single->get('web'))}
              {foreach from=$single->get('web') item='web' name='cdi'}
                {$web|escape|default:"&nbsp;"}{if !$smarty.foreach.cdi.last}, {/if}
              {foreachelse}
                &nbsp;
              {/foreach}
            {else}
              &nbsp;
            {/if}
          </td>
          {capture assign='var_value'}{$single->getVarValue('abilitys')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.abilitys}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          {capture assign='var_value'}{$single->getVarValue('ticket_email_from')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.ticket_email_from}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          <td style="mso-number-format: \@;">
          {if is_array($single->get('othercontact'))}
            {foreach from=$single->get('othercontact') item='v' name='vi'}
              {if $v}{$v|escape}{if !$smarty.foreach.vi.last}, {/if}{/if}
            {foreachelse}
              &nbsp;
            {/foreach}
          {else}
            &nbsp;
          {/if}
          </td>
          <td style="mso-number-format: \@;">{$single->get('ucn')|escape|default:"&nbsp;"}</td>
          <td style="mso-number-format: \@;">&#91;{$single->get('code')|escape|default:"&nbsp;"}&#93; {$single->get('name')|escape|default:"&nbsp;"}{if $single->get('lastname')} {$single->get('lastname')}{/if}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('comments')}{$single->get('comments')}{/if}
            
            {if $single->checkPermissions('comments_add')}
            
              
            
            {/if}
          </td>
          {capture assign='var_value'}{$single->getVarValue('working_from')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.working_from}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>
          {strip}
            {assign var='_module' value=$single->getModule()}
            {assign var='_controller' value=$single->getController()}
          {/strip}
          <td style="mso-number-format: \@;">
            
              {if $single->get('emails')}{$single->get('emails')}{/if}
            
            {if $single->checkPermissions('emails_add')}
            
              
            
            {/if}
          </td>
          {capture assign='var_value'}{$single->getVarValue('empl_picture')}{/capture}
          {if is_numeric($var_value)}{assign var='var_value' value=$var_value|replace:".":","}{/if}
          {capture assign='var_back_label'}{$add_vars_back_labels.1.empl_picture}{/capture}
          {capture assign='content'}{if $var_value || $var_value === '0'}{$var_value|nl2br|url2href}{include file="_back_label.html" back_label=$var_back_label}{else}&nbsp;{/if}{/capture}
          <td{if is_numeric($var_value)} align="right"{/if}>{$content}</td>

          </tr>
        {/if}
      {foreachelse}
        <tr>
          <td colspan="30">{#no_items_found#|escape}</td>
        </tr>
      {/foreach}
    </table>
